import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Package, Search, Filter, ExternalLink, ArrowLeft, Star, Users } from "lucide-react";
import { Link } from "react-router-dom";

interface Product {
  id: string;
  nome: string;
  empresa: string;
  descricao: string;
  categoria: string;
  popularidade: number;
  logoUrl?: string;
  comissao: string;
  rating: number;
  totalCanais: number;
}

// Mock data - em produção virá da API
const mockProducts: Product[] = [
  {
    id: "1",
    nome: "SaaS de Gestão Pro",
    empresa: "TechCorp",
    descricao: "Plataforma completa de gestão empresarial com IA integrada",
    categoria: "Software",
    popularidade: 95,
    comissao: "30%",
    rating: 4.8,
    totalCanais: 245
  },
  {
    id: "2",
    nome: "Curso de Marketing Digital",
    empresa: "EduTech",
    descricao: "Curso completo com certificação em marketing digital",
    categoria: "Educação",
    popularidade: 88,
    comissao: "50%",
    rating: 4.9,
    totalCanais: 189
  },
  {
    id: "3",
    nome: "Ferramenta de Design",
    empresa: "CreativeSoft",
    descricao: "Editor profissional para criação de designs e logos",
    categoria: "Design",
    popularidade: 92,
    comissao: "25%",
    rating: 4.7,
    totalCanais: 156
  },
  {
    id: "4",
    nome: "App de Fitness",
    empresa: "HealthTech",
    descricao: "Aplicativo completo para treinos e acompanhamento nutricional",
    categoria: "Saúde",
    popularidade: 85,
    comissao: "40%",
    rating: 4.6,
    totalCanais: 298
  },
  {
    id: "5",
    nome: "E-book de Investimentos",
    empresa: "FinanceHub",
    descricao: "Guia completo para iniciantes em investimentos",
    categoria: "Finanças",
    popularidade: 78,
    comissao: "60%",
    rating: 4.5,
    totalCanais: 134
  },
  {
    id: "6",
    nome: "Plugin WordPress",
    empresa: "WebTools",
    descricao: "Plugin para otimização de SEO e performance",
    categoria: "Tecnologia",
    popularidade: 82,
    comissao: "35%",
    rating: 4.4,
    totalCanais: 87
  }
];

const categorias = ["Todos", "Software", "Educação", "Design", "Saúde", "Finanças", "Tecnologia"];

const Vitrine = () => {
  const [products, setProducts] = useState<Product[]>(mockProducts);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>(mockProducts);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("Todos");
  const [sortBy, setSortBy] = useState("popularidade");

  useEffect(() => {
    let filtered = products;

    // Filtrar por categoria
    if (selectedCategory !== "Todos") {
      filtered = filtered.filter(product => product.categoria === selectedCategory);
    }

    // Filtrar por termo de busca
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.empresa.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.descricao.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Ordenar
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "popularidade":
          return b.popularidade - a.popularidade;
        case "rating":
          return b.rating - a.rating;
        case "canais":
          return b.totalCanais - a.totalCanais;
        case "nome":
          return a.nome.localeCompare(b.nome);
        default:
          return 0;
      }
    });

    setFilteredProducts(filtered);
  }, [products, searchTerm, selectedCategory, sortBy]);

  const getPopularityColor = (popularity: number) => {
    if (popularity >= 90) return "bg-success";
    if (popularity >= 80) return "bg-primary";
    if (popularity >= 70) return "bg-warning";
    return "bg-muted";
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center justify-between">
            <Button variant="ghost" asChild>
              <Link to="/">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Link>
            </Button>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Package className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-foreground">ProductHub</span>
            </div>
            <Button variant="outline" asChild>
              <Link to="/canal/cadastro">Ser Canal</Link>
            </Button>
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        <div className="space-y-8">
          {/* Header da Vitrine */}
          <div className="text-center space-y-4">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground">
              Vitrine de Produtos
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Descubra produtos incríveis para divulgar em seus canais e gere renda extra
            </p>
          </div>

          {/* Filtros e Busca */}
          <Card className="shadow-medium">
            <CardHeader>
              <div className="flex flex-col lg:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Buscar produtos, empresas ou categorias..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex gap-4">
                  <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                    <SelectTrigger className="w-40">
                      <Filter className="w-4 h-4 mr-2" />
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {categorias.map(categoria => (
                        <SelectItem key={categoria} value={categoria}>
                          {categoria}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <Select value={sortBy} onValueChange={setSortBy}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="popularidade">Popularidade</SelectItem>
                      <SelectItem value="rating">Avaliação</SelectItem>
                      <SelectItem value="canais">Mais Divulgado</SelectItem>
                      <SelectItem value="nome">Nome A-Z</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Estatísticas */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="text-center p-4">
              <div className="text-2xl font-bold text-primary">{filteredProducts.length}</div>
              <div className="text-sm text-muted-foreground">Produtos Encontrados</div>
            </Card>
            <Card className="text-center p-4">
              <div className="text-2xl font-bold text-accent">{categorias.length - 1}</div>
              <div className="text-sm text-muted-foreground">Categorias</div>
            </Card>
            <Card className="text-center p-4">
              <div className="text-2xl font-bold text-success">
                {filteredProducts.reduce((acc, p) => acc + p.totalCanais, 0)}
              </div>
              <div className="text-sm text-muted-foreground">Canais Ativos</div>
            </Card>
            <Card className="text-center p-4">
              <div className="text-2xl font-bold text-warning">
                {Math.round(filteredProducts.reduce((acc, p) => acc + p.rating, 0) / filteredProducts.length * 10) / 10}
              </div>
              <div className="text-sm text-muted-foreground">Avaliação Média</div>
            </Card>
          </div>

          {/* Grid de Produtos */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredProducts.map((product) => (
              <Card key={product.id} className="group hover:shadow-strong transition-all duration-300 hover:-translate-y-1">
                <CardHeader className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center">
                        <Package className="w-6 h-6 text-white" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-foreground line-clamp-1">{product.nome}</h3>
                        <p className="text-sm text-muted-foreground">{product.empresa}</p>
                      </div>
                    </div>
                    <Badge variant="secondary">{product.categoria}</Badge>
                  </div>
                  
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {product.descricao}
                  </p>
                </CardHeader>

                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center space-x-1">
                      <Star className="w-4 h-4 text-warning fill-current" />
                      <span className="font-medium">{product.rating}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Users className="w-4 h-4 text-muted-foreground" />
                      <span>{product.totalCanais} canais</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Popularidade</span>
                      <span className="text-sm font-medium">{product.popularidade}%</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${getPopularityColor(product.popularidade)}`}
                        style={{ width: `${product.popularidade}%` }}
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-2">
                    <div>
                      <div className="text-sm text-muted-foreground">Comissão</div>
                      <div className="text-lg font-bold text-accent">{product.comissao}</div>
                    </div>
                    <Button asChild>
                      <Link to="/canal/cadastro" state={{ productId: product.id }}>
                        Quero Divulgar
                        <ExternalLink className="w-4 h-4 ml-2" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredProducts.length === 0 && (
            <Card className="text-center py-12">
              <CardContent>
                <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-foreground mb-2">Nenhum produto encontrado</h3>
                <p className="text-muted-foreground mb-4">
                  Tente ajustar seus filtros ou termo de busca
                </p>
                <Button variant="outline" onClick={() => {
                  setSearchTerm("");
                  setSelectedCategory("Todos");
                  setSortBy("popularidade");
                }}>
                  Limpar Filtros
                </Button>
              </CardContent>
            </Card>
          )}

          {/* CTA Section */}
          <Card className="bg-gradient-hero text-white">
            <CardContent className="py-12 text-center">
              <h2 className="text-2xl lg:text-3xl font-bold mb-4">
                Tem um produto para divulgar?
              </h2>
              <p className="text-xl mb-8 text-white/90">
                Cadastre seu produto e alcance milhares de canais qualificados
              </p>
              <Button variant="secondary" size="lg" asChild>
                <Link to="/produto/cadastro">Cadastrar Produto</Link>
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Vitrine;