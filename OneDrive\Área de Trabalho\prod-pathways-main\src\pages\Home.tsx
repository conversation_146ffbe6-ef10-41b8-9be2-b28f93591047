import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, Users, Package, Zap } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import heroImage from "@/assets/hero-illustration.jpg";
import TestWebhook from "@/components/TestWebhook";

const Home = () => {
  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Package className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-foreground">ProductHub</span>
            </div>
            <div className="hidden md:flex items-center space-x-6">
              <Link to="/vitrine" className="text-muted-foreground hover:text-foreground transition-colors">
                Produtos
              </Link>
              <Link to="/canal/cadastro" className="text-muted-foreground hover:text-foreground transition-colors">
                Para Canais
              </Link>
              <Button variant="outline" size="sm" asChild>
                <Link to="/canal/dashboard">Dashboard</Link>
              </Button>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h1 className="text-4xl lg:text-6xl font-bold text-foreground leading-tight">
                  Conectamos <span className="bg-gradient-hero bg-clip-text text-transparent">produtos</span> aos melhores canais
                </h1>
                <p className="text-xl text-muted-foreground leading-relaxed">
                  A plataforma que facilita parcerias entre empresas e criadores de conteúdo. 
                  Cadastre seus produtos ou torne-se um canal de divulgação.
                </p>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <Button variant="hero" size="lg" asChild>
                  <Link to="/produto/cadastro">
                    Cadastrar Produto
                    <ArrowRight className="ml-2 w-5 h-5" />
                  </Link>
                </Button>
                <Button variant="accent" size="lg" asChild>
                  <Link to="/canal/cadastro">
                    Ser Canal de Divulgação
                    <Users className="ml-2 w-5 h-5" />
                  </Link>
                </Button>
              </div>

              <div className="grid grid-cols-3 gap-8 pt-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">500+</div>
                  <div className="text-sm text-muted-foreground">Produtos</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">1200+</div>
                  <div className="text-sm text-muted-foreground">Canais</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">98%</div>
                  <div className="text-sm text-muted-foreground">Satisfação</div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="relative rounded-2xl overflow-hidden shadow-strong">
                <img 
                  src={heroImage} 
                  alt="Plataforma de curadoria de produtos" 
                  className="w-full h-auto"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent" />
              </div>
              <div className="absolute -top-6 -right-6 w-24 h-24 bg-accent rounded-full flex items-center justify-center shadow-medium">
                <Zap className="w-12 h-12 text-accent-foreground" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-card">
        <div className="container mx-auto px-4">
          <div className="text-center space-y-4 mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-foreground">
              Como funciona?
            </h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Um processo simples e eficiente para conectar produtos aos melhores canais de divulgação
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-gradient-primary rounded-2xl flex items-center justify-center mx-auto shadow-medium">
                <Package className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-foreground">Cadastre seu Produto</h3>
              <p className="text-muted-foreground">
                Envie as informações do seu produto e aguarde nossa curadoria em até 24 horas
              </p>
            </div>

            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-gradient-accent rounded-2xl flex items-center justify-center mx-auto shadow-medium">
                <Users className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-foreground">Conecte-se aos Canais</h3>
              <p className="text-muted-foreground">
                Criadores de conteúdo escolhem produtos alinhados com seu público
              </p>
            </div>

            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-gradient-hero rounded-2xl flex items-center justify-center mx-auto shadow-medium">
                <Zap className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-foreground">Resultados Garantidos</h3>
              <p className="text-muted-foreground">
                Acompanhe performance e resultados através do dashboard inteligente
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Webhook Test Section - Only visible in development */}
      {process.env.NODE_ENV === 'development' && (
        <section className="py-20 bg-muted">
          <div className="container mx-auto px-4">
            <div className="text-center space-y-4 mb-8">
              <h2 className="text-2xl font-bold text-foreground">
                🔧 Teste de Backend (Dev)
              </h2>
              <p className="text-muted-foreground">
                Teste a conectividade com o webhook do backend
              </p>
            </div>
            <div className="flex justify-center">
              <TestWebhook />
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-20 bg-gradient-hero">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto space-y-8">
            <h2 className="text-3xl lg:text-4xl font-bold text-white">
              Pronto para crescer seu negócio?
            </h2>
            <p className="text-xl text-white/90">
              Junte-se a centenas de empresas e criadores que já estão crescendo juntos
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" size="lg" className="bg-white text-primary hover:bg-white/90" asChild>
                <Link to="/vitrine">Ver Produtos Disponíveis</Link>
              </Button>
              <Button variant="secondary" size="lg" asChild>
                <Link to="/produto/cadastro">Começar Agora</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-12 bg-card border-t">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Package className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-foreground">ProductHub</span>
            </div>
            <div className="text-sm text-muted-foreground">
              © 2024 ProductHub. Conectando produtos aos melhores canais.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Home;