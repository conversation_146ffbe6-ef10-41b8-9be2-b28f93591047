import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Package, ArrowLeft, CheckCircle, AlertCircle, Loader2 } from "lucide-react";
import { Link } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { sendProductData } from "@/lib/webhook";

interface FormData {
  empresa: string;
  produto: string;
  descricao: string;
  linkAfiliado: string;
  contato: string;
}

const CadastroProduto = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState<FormData>({
    empresa: "",
    produto: "",
    descricao: "",
    linkAfiliado: "",
    contato: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState<Partial<FormData>>({});

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};

    if (!formData.empresa.trim()) {
      newErrors.empresa = "Nome da empresa é obrigatório";
    }
    if (!formData.produto.trim()) {
      newErrors.produto = "Nome do produto é obrigatório";
    }
    if (!formData.descricao.trim()) {
      newErrors.descricao = "Descrição é obrigatória";
    }
    if (!formData.linkAfiliado.trim()) {
      newErrors.linkAfiliado = "Link de afiliado é obrigatório";
    } else if (!formData.linkAfiliado.match(/^https?:\/\/.+/)) {
      newErrors.linkAfiliado = "Digite um link válido (deve começar com http ou https)";
    }
    if (!formData.contato.trim()) {
      newErrors.contato = "Contato é obrigatório";
    } else if (!formData.contato.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      newErrors.contato = "Digite um e-mail válido";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      toast({
        title: "Erro no formulário",
        description: "Por favor, corrija os campos destacados",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      console.log("Iniciando envio dos dados do produto...");
      
      // Enviar dados reais para o webhook
      const success = await sendProductData(formData);
      
      if (success) {
        console.log("Produto enviado com sucesso!");
        setIsSubmitted(true);
        toast({
          title: "Produto cadastrado!",
          description: "Seu produto foi enviado e será avaliado em até 24 horas",
        });
      } else {
        throw new Error("Falha no envio dos dados");
      }
    } catch (error) {
      console.error("Erro detalhado:", error);
      toast({
        title: "Erro ao enviar",
        description: "Verifique sua conexão e tente novamente",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-muted flex items-center justify-center px-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-success rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="w-8 h-8 text-success-foreground" />
              </div>
              <div className="space-y-2">
                <h2 className="text-2xl font-bold text-foreground">Produto Enviado!</h2>
                <p className="text-muted-foreground">
                  Seu produto foi enviado com sucesso e será avaliado em até 24 horas. 
                  Você receberá um e-mail com o resultado da avaliação.
                </p>
              </div>
              <div className="flex flex-col gap-2">
                <Button asChild>
                  <Link to="/">Voltar ao Início</Link>
                </Button>
                <Button variant="outline" onClick={() => {
                  setIsSubmitted(false);
                  setFormData({
                    empresa: "",
                    produto: "",
                    descricao: "",
                    linkAfiliado: "",
                    contato: ""
                  });
                }}>
                  Cadastrar Outro Produto
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center justify-between">
            <Button variant="ghost" asChild>
              <Link to="/">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Link>
            </Button>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Package className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-foreground">ProductHub</span>
            </div>
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto">
          <div className="text-center space-y-4 mb-8">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground">
              Cadastrar Produto
            </h1>
            <p className="text-xl text-muted-foreground">
              Preencha as informações do seu produto para análise da nossa equipe
            </p>
          </div>

          <Card className="shadow-medium">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="w-5 h-5 text-primary" />
                Informações do Produto
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="empresa">Nome da Empresa *</Label>
                    <Input
                      id="empresa"
                      placeholder="Ex: TechCorp Ltda"
                      value={formData.empresa}
                      onChange={(e) => handleInputChange("empresa", e.target.value)}
                      className={errors.empresa ? "border-destructive" : ""}
                    />
                    {errors.empresa && (
                      <div className="flex items-center gap-1 text-sm text-destructive">
                        <AlertCircle className="w-4 h-4" />
                        {errors.empresa}
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="produto">Nome do Produto *</Label>
                    <Input
                      id="produto"
                      placeholder="Ex: SaaS de Gestão"
                      value={formData.produto}
                      onChange={(e) => handleInputChange("produto", e.target.value)}
                      className={errors.produto ? "border-destructive" : ""}
                    />
                    {errors.produto && (
                      <div className="flex items-center gap-1 text-sm text-destructive">
                        <AlertCircle className="w-4 h-4" />
                        {errors.produto}
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="descricao">Descrição Curta *</Label>
                  <Textarea
                    id="descricao"
                    placeholder="Descreva seu produto em poucas palavras (máx. 200 caracteres)"
                    value={formData.descricao}
                    onChange={(e) => handleInputChange("descricao", e.target.value)}
                    maxLength={200}
                    className={errors.descricao ? "border-destructive" : ""}
                  />
                  <div className="flex justify-between">
                    {errors.descricao && (
                      <div className="flex items-center gap-1 text-sm text-destructive">
                        <AlertCircle className="w-4 h-4" />
                        {errors.descricao}
                      </div>
                    )}
                    <div className="text-sm text-muted-foreground ml-auto">
                      {formData.descricao.length}/200
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="linkAfiliado">Link de Afiliado *</Label>
                  <Input
                    id="linkAfiliado"
                    type="url"
                    placeholder="https://exemplo.com/affiliate/123"
                    value={formData.linkAfiliado}
                    onChange={(e) => handleInputChange("linkAfiliado", e.target.value)}
                    className={errors.linkAfiliado ? "border-destructive" : ""}
                  />
                  {errors.linkAfiliado && (
                    <div className="flex items-center gap-1 text-sm text-destructive">
                      <AlertCircle className="w-4 h-4" />
                      {errors.linkAfiliado}
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="contato">E-mail de Contato *</Label>
                  <Input
                    id="contato"
                    type="email"
                    placeholder="<EMAIL>"
                    value={formData.contato}
                    onChange={(e) => handleInputChange("contato", e.target.value)}
                    className={errors.contato ? "border-destructive" : ""}
                  />
                  {errors.contato && (
                    <div className="flex items-center gap-1 text-sm text-destructive">
                      <AlertCircle className="w-4 h-4" />
                      {errors.contato}
                    </div>
                  )}
                </div>

                <div className="bg-muted p-4 rounded-lg">
                  <h3 className="font-semibold text-foreground mb-2">Processo de Avaliação</h3>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Análise da qualidade e relevância do produto</li>
                    <li>• Verificação do link de afiliado</li>
                    <li>• Validação das informações fornecidas</li>
                    <li>• Resposta em até 24 horas por e-mail</li>
                  </ul>
                </div>

                <Button 
                  type="submit" 
                  className="w-full" 
                  size="lg"
                  disabled={isSubmitting}
                >
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isSubmitting ? "Enviando para o servidor..." : "Enviar para Análise"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CadastroProduto;