import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Copy, Package, ExternalLink, ArrowLeft, Search, TrendingUp, Users, DollarSign, Eye } from "lucide-react";
import { Link } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";

interface Product {
  id: string;
  nome: string;
  empresa: string;
  categoria: string;
  comissao: string;
  linkAfiliado: string;
  clicks: number;
  conversoes: number;
  earnings: number;
  status: "ativo" | "pausado";
}

// Mock data - produtos do canal
const mockUserProducts: Product[] = [
  {
    id: "1",
    nome: "SaaS de Gestão Pro",
    empresa: "TechCorp",
    categoria: "Software",
    comissao: "30%",
    linkAfiliado: "https://techcorp.com/affiliate/user123/saas-gestao",
    clicks: 450,
    conversoes: 23,
    earnings: 1250.80,
    status: "ativo"
  },
  {
    id: "2",
    nome: "Curso de Marketing Digital",
    empresa: "EduTech",
    categoria: "Educação",
    comissao: "50%",
    linkAfiliado: "https://edutech.com/affiliate/user123/marketing-course",
    clicks: 320,
    conversoes: 18,
    earnings: 890.50,
    status: "ativo"
  },
  {
    id: "4",
    nome: "App de Fitness",
    empresa: "HealthTech",
    categoria: "Saúde",
    comissao: "40%",
    linkAfiliado: "https://healthtech.com/affiliate/user123/fitness-app",
    clicks: 180,
    conversoes: 8,
    earnings: 320.00,
    status: "pausado"
  }
];

const Dashboard = () => {
  const { toast } = useToast();
  const [products] = useState<Product[]>(mockUserProducts);
  const [searchTerm, setSearchTerm] = useState("");

  const filteredProducts = products.filter(product =>
    product.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
    product.empresa.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalClicks = products.reduce((acc, p) => acc + p.clicks, 0);
  const totalConversoes = products.reduce((acc, p) => acc + p.conversoes, 0);
  const totalEarnings = products.reduce((acc, p) => acc + p.earnings, 0);
  const conversionRate = totalClicks > 0 ? ((totalConversoes / totalClicks) * 100).toFixed(1) : "0";

  const copyToClipboard = (link: string, productName: string) => {
    navigator.clipboard.writeText(link);
    toast({
      title: "Link copiado!",
      description: `Link do ${productName} copiado para a área de transferência`,
    });
  };

  const getStatusColor = (status: string) => {
    return status === "ativo" ? "bg-success" : "bg-warning";
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center justify-between">
            <Button variant="ghost" asChild>
              <Link to="/">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Início
              </Link>
            </Button>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Package className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-foreground">ProductHub</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" asChild>
                <Link to="/vitrine">Explorar Produtos</Link>
              </Button>
            </div>
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        <div className="space-y-8">
          {/* Header do Dashboard */}
          <div className="text-center space-y-4">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground">
              Dashboard do Canal
            </h1>
            <p className="text-xl text-muted-foreground">
              Gerencie seus produtos e acompanhe sua performance
            </p>
          </div>

          {/* Estatísticas Gerais */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Eye className="w-5 h-5 text-primary" />
                  <div>
                    <div className="text-2xl font-bold text-foreground">{totalClicks.toLocaleString()}</div>
                    <div className="text-sm text-muted-foreground">Total de Clicks</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="w-5 h-5 text-accent" />
                  <div>
                    <div className="text-2xl font-bold text-foreground">{totalConversoes}</div>
                    <div className="text-sm text-muted-foreground">Conversões</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Users className="w-5 h-5 text-success" />
                  <div>
                    <div className="text-2xl font-bold text-foreground">{conversionRate}%</div>
                    <div className="text-sm text-muted-foreground">Taxa de Conversão</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <DollarSign className="w-5 h-5 text-warning" />
                  <div>
                    <div className="text-2xl font-bold text-foreground">
                      R$ {totalEarnings.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                    </div>
                    <div className="text-sm text-muted-foreground">Ganhos Totais</div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Filtros */}
          <Card>
            <CardHeader>
              <div className="flex flex-col sm:flex-row gap-4 items-center">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Buscar produtos..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant="secondary">
                    {filteredProducts.length} produtos
                  </Badge>
                </div>
              </div>
            </CardHeader>
          </Card>

          {/* Lista de Produtos */}
          <div className="space-y-4">
            {filteredProducts.map((product) => (
              <Card key={product.id} className="hover:shadow-medium transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row lg:items-center gap-6">
                    {/* Informações do Produto */}
                    <div className="flex-1 space-y-2">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-gradient-primary rounded-lg flex items-center justify-center">
                          <Package className="w-5 h-5 text-white" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-foreground">{product.nome}</h3>
                          <p className="text-sm text-muted-foreground">{product.empresa}</p>
                        </div>
                        <Badge variant="secondary">{product.categoria}</Badge>
                        <Badge className={getStatusColor(product.status)}>
                          {product.status}
                        </Badge>
                      </div>
                      
                      <div className="bg-muted p-3 rounded-lg">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-foreground">Link de Afiliado:</span>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(product.linkAfiliado, product.nome)}
                          >
                            <Copy className="w-4 h-4 mr-1" />
                            Copiar
                          </Button>
                        </div>
                        <div className="text-sm text-muted-foreground mt-1 break-all">
                          {product.linkAfiliado}
                        </div>
                      </div>
                    </div>

                    {/* Estatísticas */}
                    <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:min-w-0 lg:w-80">
                      <div className="text-center">
                        <div className="text-lg font-bold text-primary">{product.clicks}</div>
                        <div className="text-xs text-muted-foreground">Clicks</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-accent">{product.conversoes}</div>
                        <div className="text-xs text-muted-foreground">Conversões</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-success">{product.comissao}</div>
                        <div className="text-xs text-muted-foreground">Comissão</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-warning">
                          R$ {product.earnings.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
                        </div>
                        <div className="text-xs text-muted-foreground">Ganhos</div>
                      </div>
                    </div>

                    {/* Ações */}
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm" asChild>
                        <a href={product.linkAfiliado} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-4 h-4 mr-1" />
                          Visitar
                        </a>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {filteredProducts.length === 0 && (
            <Card className="text-center py-12">
              <CardContent>
                <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold text-foreground mb-2">
                  {searchTerm ? "Nenhum produto encontrado" : "Nenhum produto cadastrado"}
                </h3>
                <p className="text-muted-foreground mb-4">
                  {searchTerm 
                    ? "Tente ajustar seu termo de busca"
                    : "Explore nossa vitrine e comece a divulgar produtos incríveis"
                  }
                </p>
                <Button asChild>
                  <Link to="/vitrine">Explorar Produtos</Link>
                </Button>
              </CardContent>
            </Card>
          )}

          {/* CTA para novos produtos */}
          {filteredProducts.length > 0 && (
            <Card className="bg-gradient-hero text-white">
              <CardContent className="py-8 text-center">
                <h2 className="text-2xl font-bold mb-4">
                  Quer divulgar mais produtos?
                </h2>
                <p className="text-xl mb-6 text-white/90">
                  Explore nossa vitrine e encontre produtos perfeitos para seu público
                </p>
                <Button variant="secondary" size="lg" asChild>
                  <Link to="/vitrine">Ver Mais Produtos</Link>
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
};

export default Dashboard;