import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Users, ArrowLeft, CheckCircle, AlertCircle, Package, Loader2 } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { useToast } from "@/hooks/use-toast";
import { sendCanalData } from "@/lib/webhook";

interface FormData {
  nome: string;
  email: string;
  nicho: string;
  descricaoCanal: string;
  plataformas: string[];
  produtos: string[];
}

interface Product {
  id: string;
  nome: string;
  empresa: string;
  categoria: string;
  comissao: string;
}

// Mock data - produtos disponíveis
const mockProducts: Product[] = [
  { id: "1", nome: "SaaS de Gestão Pro", empresa: "TechCorp", categoria: "Software", comissao: "30%" },
  { id: "2", nome: "Curso de Marketing Digital", empresa: "EduTech", categoria: "Educação", comissao: "50%" },
  { id: "3", nome: "Ferramenta de Design", empresa: "CreativeSoft", categoria: "Design", comissao: "25%" },
  { id: "4", nome: "App de Fitness", empresa: "HealthTech", categoria: "Saúde", comissao: "40%" },
  { id: "5", nome: "E-book de Investimentos", empresa: "FinanceHub", categoria: "Finanças", comissao: "60%" },
  { id: "6", nome: "Plugin WordPress", empresa: "WebTools", categoria: "Tecnologia", comissao: "35%" }
];

const nichos = [
  "Tecnologia",
  "Marketing Digital",
  "Empreendedorismo",
  "Finanças",
  "Saúde e Fitness",
  "Educação",
  "Lifestyle",
  "Design",
  "Desenvolvimento",
  "Outros"
];

const plataformasDisponiveis = [
  "YouTube",
  "Instagram",
  "TikTok",
  "LinkedIn",
  "Twitter/X",
  "Facebook",
  "Blog/Site",
  "Podcast",
  "Newsletter",
  "Telegram"
];

const CadastroCanal = () => {
  const { toast } = useToast();
  const location = useLocation();
  const [formData, setFormData] = useState<FormData>({
    nome: "",
    email: "",
    nicho: "",
    descricaoCanal: "",
    plataformas: [],
    produtos: []
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [selectedProducts, setSelectedProducts] = useState<string[]>([]);

  // Se veio da vitrine com um produto específico, pré-seleciona
  useEffect(() => {
    if (location.state?.productId) {
      setSelectedProducts([location.state.productId]);
      setFormData(prev => ({ ...prev, produtos: [location.state.productId] }));
    }
  }, [location.state]);

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};

    if (!formData.nome.trim()) {
      newErrors.nome = "Nome é obrigatório";
    }
    if (!formData.email.trim()) {
      newErrors.email = "E-mail é obrigatório";
    } else if (!formData.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
      newErrors.email = "Digite um e-mail válido";
    }
    if (!formData.nicho) {
      newErrors.nicho = "Selecione um nicho";
    }
    if (!formData.descricaoCanal.trim()) {
      newErrors.descricaoCanal = "Descrição do canal é obrigatória";
    }
    if (formData.plataformas.length === 0) {
      newErrors.plataformas = ["Selecione pelo menos uma plataforma"] as any;
    }
    if (selectedProducts.length === 0) {
      newErrors.produtos = ["Selecione pelo menos um produto"] as any;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const finalFormData = { ...formData, produtos: selectedProducts };
    
    if (!validateForm()) {
      toast({
        title: "Erro no formulário",
        description: "Por favor, corrija os campos destacados",
        variant: "destructive"
      });
      return;
    }

    setIsSubmitting(true);

    try {
      console.log("Iniciando envio dos dados do canal...");
      
      // Enviar dados reais para o webhook
      const success = await sendCanalData(finalFormData);
      
      if (success) {
        console.log("Canal cadastrado com sucesso!");
        setIsSubmitted(true);
        toast({
          title: "Cadastro realizado!",
          description: "Você receberá os links para divulgação por e-mail",
        });
      } else {
        throw new Error("Falha no envio dos dados");
      }
    } catch (error) {
      console.error("Erro detalhado:", error);
      toast({
        title: "Erro ao cadastrar",
        description: "Verifique sua conexão e tente novamente",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string | string[]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handlePlataformaChange = (plataforma: string, checked: boolean) => {
    const newPlataformas = checked 
      ? [...formData.plataformas, plataforma]
      : formData.plataformas.filter(p => p !== plataforma);
    
    handleInputChange("plataformas", newPlataformas);
  };

  const handleProductChange = (productId: string, checked: boolean) => {
    const newProducts = checked 
      ? [...selectedProducts, productId]
      : selectedProducts.filter(p => p !== productId);
    
    setSelectedProducts(newProducts);
    if (errors.produtos) {
      setErrors(prev => ({ ...prev, produtos: undefined }));
    }
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-background to-muted flex items-center justify-center px-4">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-success rounded-full flex items-center justify-center mx-auto">
                <CheckCircle className="w-8 h-8 text-success-foreground" />
              </div>
              <div className="space-y-2">
                <h2 className="text-2xl font-bold text-foreground">Cadastro Realizado!</h2>
                <p className="text-muted-foreground">
                  Seu cadastro foi realizado com sucesso! Você receberá os links 
                  para divulgação dos produtos selecionados por e-mail em breve.
                </p>
              </div>
              <div className="flex flex-col gap-2">
                <Button asChild>
                  <Link to="/canal/dashboard">Acessar Dashboard</Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link to="/">Voltar ao Início</Link>
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted">
      {/* Header */}
      <header className="border-b bg-card/50 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center justify-between">
            <Button variant="ghost" asChild>
              <Link to="/">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Voltar
              </Link>
            </Button>
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center">
                <Package className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-bold text-foreground">ProductHub</span>
            </div>
          </nav>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        <div className="max-w-4xl mx-auto">
          <div className="text-center space-y-4 mb-8">
            <h1 className="text-3xl lg:text-4xl font-bold text-foreground">
              Cadastro de Canal
            </h1>
            <p className="text-xl text-muted-foreground">
              Torne-se parceiro e comece a divulgar produtos de qualidade
            </p>
          </div>

          <Card className="shadow-medium">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5 text-primary" />
                Informações do Canal
              </CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Informações Básicas */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="nome">Nome Completo *</Label>
                    <Input
                      id="nome"
                      placeholder="Seu nome completo"
                      value={formData.nome}
                      onChange={(e) => handleInputChange("nome", e.target.value)}
                      className={errors.nome ? "border-destructive" : ""}
                    />
                    {errors.nome && (
                      <div className="flex items-center gap-1 text-sm text-destructive">
                        <AlertCircle className="w-4 h-4" />
                        {errors.nome}
                      </div>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email">E-mail *</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      className={errors.email ? "border-destructive" : ""}
                    />
                    {errors.email && (
                      <div className="flex items-center gap-1 text-sm text-destructive">
                        <AlertCircle className="w-4 h-4" />
                        {errors.email}
                      </div>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nicho">Nicho/Área de Atuação *</Label>
                  <Select value={formData.nicho} onValueChange={(value) => handleInputChange("nicho", value)}>
                    <SelectTrigger className={errors.nicho ? "border-destructive" : ""}>
                      <SelectValue placeholder="Selecione seu nicho principal" />
                    </SelectTrigger>
                    <SelectContent>
                      {nichos.map(nicho => (
                        <SelectItem key={nicho} value={nicho}>{nicho}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.nicho && (
                    <div className="flex items-center gap-1 text-sm text-destructive">
                      <AlertCircle className="w-4 h-4" />
                      {errors.nicho}
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="descricaoCanal">Descrição do Canal *</Label>
                  <Textarea
                    id="descricaoCanal"
                    placeholder="Descreva seu canal, público-alvo e tipo de conteúdo que produz"
                    value={formData.descricaoCanal}
                    onChange={(e) => handleInputChange("descricaoCanal", e.target.value)}
                    className={errors.descricaoCanal ? "border-destructive" : ""}
                    rows={4}
                  />
                  {errors.descricaoCanal && (
                    <div className="flex items-center gap-1 text-sm text-destructive">
                      <AlertCircle className="w-4 h-4" />
                      {errors.descricaoCanal}
                    </div>
                  )}
                </div>

                {/* Plataformas */}
                <div className="space-y-4">
                  <Label>Plataformas onde você atua *</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {plataformasDisponiveis.map((plataforma) => (
                      <div key={plataforma} className="flex items-center space-x-2">
                        <Checkbox
                          id={plataforma}
                          checked={formData.plataformas.includes(plataforma)}
                          onCheckedChange={(checked) => 
                            handlePlataformaChange(plataforma, checked as boolean)
                          }
                        />
                        <Label htmlFor={plataforma} className="text-sm font-normal">
                          {plataforma}
                        </Label>
                      </div>
                    ))}
                  </div>
                  {errors.plataformas && (
                    <div className="flex items-center gap-1 text-sm text-destructive">
                      <AlertCircle className="w-4 h-4" />
                      {errors.plataformas}
                    </div>
                  )}
                </div>

                {/* Seleção de Produtos */}
                <div className="space-y-4">
                  <Label>Produtos que deseja divulgar *</Label>
                  <div className="grid gap-4">
                    {mockProducts.map((product) => (
                      <Card key={product.id} className={`cursor-pointer transition-all ${
                        selectedProducts.includes(product.id) 
                          ? 'ring-2 ring-primary bg-primary/5' 
                          : 'hover:shadow-medium'
                      }`}>
                        <CardContent className="p-4">
                          <div className="flex items-center space-x-3">
                            <Checkbox
                              id={product.id}
                              checked={selectedProducts.includes(product.id)}
                              onCheckedChange={(checked) => 
                                handleProductChange(product.id, checked as boolean)
                              }
                            />
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <div>
                                  <h4 className="font-semibold text-foreground">{product.nome}</h4>
                                  <p className="text-sm text-muted-foreground">{product.empresa}</p>
                                </div>
                                <div className="text-right">
                                  <div className="text-sm text-muted-foreground">Comissão</div>
                                  <div className="font-bold text-accent">{product.comissao}</div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                  {errors.produtos && (
                    <div className="flex items-center gap-1 text-sm text-destructive">
                      <AlertCircle className="w-4 h-4" />
                      {errors.produtos}
                    </div>
                  )}
                </div>

                {/* Informações sobre o processo */}
                <div className="bg-muted p-6 rounded-lg">
                  <h3 className="font-semibold text-foreground mb-3">Próximos Passos</h3>
                  <ul className="text-sm text-muted-foreground space-y-2">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-success" />
                      Análise do seu perfil e canal
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-success" />
                      Envio dos links de afiliado por e-mail
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-success" />
                      Acesso ao dashboard com materiais de apoio
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-success" />
                      Suporte contínuo da nossa equipe
                    </li>
                  </ul>
                </div>

                <Button 
                  type="submit" 
                  className="w-full" 
                  size="lg"
                  disabled={isSubmitting}
                >
                  {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {isSubmitting ? "Enviando cadastro para o servidor..." : "Finalizar Cadastro"}
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CadastroCanal;