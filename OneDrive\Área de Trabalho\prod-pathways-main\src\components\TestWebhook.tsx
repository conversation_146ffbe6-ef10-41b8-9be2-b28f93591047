import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { testWebhook } from "@/lib/webhook";
import { useToast } from "@/hooks/use-toast";
import { Loader2, CheckCircle, XCircle, Zap } from "lucide-react";

const TestWebhook = () => {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [lastTest, setLastTest] = useState<{ success: boolean; timestamp: string } | null>(null);

  const handleTest = async () => {
    setIsLoading(true);
    
    try {
      console.log("Iniciando teste de conectividade...");
      const success = await testWebhook();
      
      const result = {
        success,
        timestamp: new Date().toLocaleString('pt-BR')
      };
      
      setLastTest(result);
      
      if (success) {
        toast({
          title: "✅ Teste bem-sucedido!",
          description: "Webhook está funcionando corretamente",
        });
      } else {
        toast({
          title: "❌ Teste falhou",
          description: "Verifique o console para mais detalhes",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error("Erro no teste:", error);
      toast({
        title: "❌ Erro no teste",
        description: "Verifique sua conexão com a internet",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="w-5 h-5 text-primary" />
          Teste de Webhook
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-sm text-muted-foreground">
          URL: <code className="bg-muted px-2 py-1 rounded text-xs">
            https://eovafv7c8sh45l5.m.pipedream.net
          </code>
        </div>
        
        <Button 
          onClick={handleTest} 
          disabled={isLoading}
          className="w-full"
        >
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          {isLoading ? "Testando..." : "Testar Conectividade"}
        </Button>

        {lastTest && (
          <div className="flex items-center justify-between p-3 bg-muted rounded-lg">
            <div className="flex items-center gap-2">
              {lastTest.success ? (
                <CheckCircle className="w-4 h-4 text-success" />
              ) : (
                <XCircle className="w-4 h-4 text-destructive" />
              )}
              <span className="text-sm">
                {lastTest.success ? "Sucesso" : "Falhou"}
              </span>
            </div>
            <Badge variant="outline" className="text-xs">
              {lastTest.timestamp}
            </Badge>
          </div>
        )}

        <div className="text-xs text-muted-foreground">
          💡 Abra o console do navegador (F12) para ver logs detalhados
        </div>
      </CardContent>
    </Card>
  );
};

export default TestWebhook;