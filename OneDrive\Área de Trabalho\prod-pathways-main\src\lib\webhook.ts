// Configuração da API e webhooks
export const API_CONFIG = {
  WEBHOOK_URL: 'https://eovafv7c8sh45l5.m.pipedream.net',
  TIMEOUT: 10000, // 10 segundos
};

// Tipos para as requisições
export interface ProductData {
  empresa: string;
  produto: string;
  descricao: string;
  linkAfiliado: string;
  contato: string;
  timestamp?: string;
  type: 'product_registration';
}

export interface CanalData {
  nome: string;
  email: string;
  nicho: string;
  descricaoCanal: string;
  plataformas: string[];
  produtos: string[];
  timestamp?: string;
  type: 'channel_registration';
}

// Função genérica para enviar dados via webhook
export const sendWebhookData = async (data: ProductData | CanalData): Promise<boolean> => {
  try {
    // Adiciona timestamp e logs para debug
    const payload = {
      ...data,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    console.log('Enviando dados para webhook:', payload);

    const response = await fetch(API_CONFIG.WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify(payload),
      signal: AbortSignal.timeout(API_CONFIG.TIMEOUT)
    });

    console.log('Resposta do webhook:', {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers.entries())
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    // Tenta ler a resposta como JSON, mas não falha se não conseguir
    try {
      const responseData = await response.json();
      console.log('Dados da resposta:', responseData);
    } catch (e) {
      console.log('Resposta não é JSON válido');
    }

    return true;
  } catch (error) {
    console.error('Erro ao enviar webhook:', error);
    
    // Log detalhado do erro
    if (error instanceof TypeError && error.message.includes('fetch')) {
      console.error('Erro de rede - verifique conectividade');
    } else if (error instanceof Error && error.name === 'AbortError') {
      console.error('Timeout - requisição demorou mais que', API_CONFIG.TIMEOUT, 'ms');
    }
    
    return false;
  }
};

// Função específica para produtos
export const sendProductData = async (productData: Omit<ProductData, 'type'>): Promise<boolean> => {
  return sendWebhookData({
    ...productData,
    type: 'product_registration'
  });
};

// Função específica para canais
export const sendCanalData = async (canalData: Omit<CanalData, 'type'>): Promise<boolean> => {
  return sendWebhookData({
    ...canalData,
    type: 'channel_registration'
  });
};

// Função para testar conectividade
export const testWebhook = async (): Promise<boolean> => {
  try {
    const testPayload = {
      type: 'connectivity_test',
      message: 'Teste de conectividade',
      timestamp: new Date().toISOString(),
      source: 'ProductHub Frontend'
    };

    console.log('Enviando teste de conectividade:', testPayload);

    const response = await fetch(API_CONFIG.WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify(testPayload),
      signal: AbortSignal.timeout(API_CONFIG.TIMEOUT)
    });

    console.log('Resposta do teste:', {
      status: response.status,
      statusText: response.statusText
    });

    return response.ok;
  } catch (error) {
    console.error('Erro no teste de conectividade:', error);
    return false;
  }
};